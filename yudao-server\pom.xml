<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yudao-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        后端 Server 的主项目，通过引入需要 yudao-module-xxx 的依赖，
        从而实现提供 RESTful API 给 yudao-ui-admin、yudao-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-system</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-infra</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 会员中心。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-member</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 数据报表。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-report</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 工作流。默认注释，保证编译速度 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-bpm</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 支付服务。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-pay</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 微信公众号模块。默认注释，保证编译速度 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-mp</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 商城相关模块。默认注释，保证编译速度-->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-product</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-promotion</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-trade</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-statistics</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- CRM 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-crm</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- ERP 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-erp</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- AI 大模型相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-ai</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- IoT 物联网相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-module-iot-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-protection</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
